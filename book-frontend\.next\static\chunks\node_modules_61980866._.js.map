{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/play/book-management-system/book-frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 13, "column": 0}, "map": {"version": 3, "file": "ellipsis.js", "sources": ["file:///D:/code/play/book-management-system/book-frontend/node_modules/lucide-react/src/icons/ellipsis.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '1', key: '41hilf' }],\n  ['circle', { cx: '19', cy: '12', r: '1', key: '1wjl8i' }],\n  ['circle', { cx: '5', cy: '12', r: '1', key: '1pcz8c' }],\n];\n\n/**\n * @component @name Ellipsis\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxIiAvPgogIDxjaXJjbGUgY3g9IjE5IiBjeT0iMTIiIHI9IjEiIC8+CiAgPGNpcmNsZSBjeD0iNSIgY3k9IjEyIiByPSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/ellipsis\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Ellipsis = createLucideIcon('ellipsis', __iconNode);\n\nexport default Ellipsis;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "file": "circle-plus.js", "sources": ["file:///D:/code/play/book-management-system/book-frontend/node_modules/lucide-react/src/icons/circle-plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'M8 12h8', key: '1wcyev' }],\n  ['path', { d: 'M12 8v8', key: 'napkw2' }],\n];\n\n/**\n * @component @name CirclePlus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJNOCAxMmg4IiAvPgogIDxwYXRoIGQ9Ik0xMiA4djgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CirclePlus = createLucideIcon('circle-plus', __iconNode);\n\nexport default CirclePlus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC1C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "file": "square-pen.js", "sources": ["file:///D:/code/play/book-management-system/book-frontend/node_modules/lucide-react/src/icons/square-pen.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7', key: '1m0v6g' }],\n  [\n    'path',\n    {\n      d: 'M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z',\n      key: 'ohrbg2',\n    },\n  ],\n];\n\n/**\n * @component @name SquarePen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM0g1YTIgMiAwIDAgMC0yIDJ2MTRhMiAyIDAgMCAwIDIgMmgxNGEyIDIgMCAwIDAgMi0ydi03IiAvPgogIDxwYXRoIGQ9Ik0xOC4zNzUgMi42MjVhMSAxIDAgMCAxIDMgM2wtOS4wMTMgOS4wMTRhMiAyIDAgMCAxLS44NTMuNTA1bC0yLjg3My44NGEuNS41IDAgMCAxLS42Mi0uNjJsLjg0LTIuODczYTIgMiAwIDAgMSAuNTA2LS44NTJ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/square-pen\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SquarePen = createLucideIcon('square-pen', __iconNode);\n\nexport default SquarePen;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3F;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/play/book-management-system/book-frontend/node_modules/%40radix-ui/react-label/src/label.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Label\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Label';\n\ntype LabelElement = React.ComponentRef<typeof Primitive.label>;\ntype PrimitiveLabelProps = React.ComponentPropsWithoutRef<typeof Primitive.label>;\ninterface LabelProps extends PrimitiveLabelProps {}\n\nconst Label = React.forwardRef<LabelElement, LabelProps>((props, forwardedRef) => {\n  return (\n    <Primitive.label\n      {...props}\n      ref={forwardedRef}\n      onMouseDown={(event) => {\n        // only prevent text selection if clicking inside the label itself\n        const target = event.target as HTMLElement;\n        if (target.closest('button, input, select, textarea')) return;\n\n        props.onMouseDown?.(event);\n        // prevent text selection when double clicking label\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }}\n    />\n  );\n});\n\nLabel.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Label;\n\nexport {\n  Label,\n  //\n  Root,\n};\nexport type { LabelProps };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AActB;;;;;AARJ,IAAM,OAAO;AAMb,IAAM,0KAAc,aAAA,EAAqC,CAAC,OAAO,iBAAiB;IAChF,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,KAAA,EAAV;QACE,GAAG,KAAA;QACJ,KAAK;QACL,aAAa,CAAC,UAAU;YAEtB,MAAM,SAAS,MAAM,MAAA;YACrB,IAAI,OAAO,OAAA,CAAQ,iCAAiC,EAAG,CAAA;YAEvD,MAAM,WAAA,GAAc,KAAK;YAEzB,IAAI,CAAC,MAAM,gBAAA,IAAoB,MAAM,MAAA,GAAS,EAAG,CAAA,MAAM,cAAA,CAAe;QACxE;IAAA;AAGN,CAAC;AAED,MAAM,WAAA,GAAc;AAIpB,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/play/book-management-system/book-frontend/node_modules/%40radix-ui/react-dialog/src/dialog.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContext, createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { RemoveScroll } from 'react-remove-scroll';\nimport { hideOthers } from 'aria-hidden';\nimport { createSlot } from '@radix-ui/react-slot';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Dialog\n * -----------------------------------------------------------------------------------------------*/\n\nconst DIALOG_NAME = 'Dialog';\n\ntype ScopedProps<P> = P & { __scopeDialog?: Scope };\nconst [createDialogContext, createDialogScope] = createContextScope(DIALOG_NAME);\n\ntype DialogContextValue = {\n  triggerRef: React.RefObject<HTMLButtonElement | null>;\n  contentRef: React.RefObject<DialogContentElement | null>;\n  contentId: string;\n  titleId: string;\n  descriptionId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DialogProvider, useDialogContext] = createDialogContext<DialogContextValue>(DIALOG_NAME);\n\ninterface DialogProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst Dialog: React.FC<DialogProps> = (props: ScopedProps<DialogProps>) => {\n  const {\n    __scopeDialog,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const contentRef = React.useRef<DialogContentElement>(null);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: DIALOG_NAME,\n  });\n\n  return (\n    <DialogProvider\n      scope={__scopeDialog}\n      triggerRef={triggerRef}\n      contentRef={contentRef}\n      contentId={useId()}\n      titleId={useId()}\n      descriptionId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      {children}\n    </DialogProvider>\n  );\n};\n\nDialog.displayName = DIALOG_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DialogTrigger';\n\ntype DialogTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DialogTriggerProps extends PrimitiveButtonProps {}\n\nconst DialogTrigger = React.forwardRef<DialogTriggerElement, DialogTriggerProps>(\n  (props: ScopedProps<DialogTriggerProps>, forwardedRef) => {\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = useComposedRefs(forwardedRef, context.triggerRef);\n    return (\n      <Primitive.button\n        type=\"button\"\n        aria-haspopup=\"dialog\"\n        aria-expanded={context.open}\n        aria-controls={context.contentId}\n        data-state={getState(context.open)}\n        {...triggerProps}\n        ref={composedTriggerRef}\n        onClick={composeEventHandlers(props.onClick, context.onOpenToggle)}\n      />\n    );\n  }\n);\n\nDialogTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DialogPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createDialogContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface DialogPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogPortal: React.FC<DialogPortalProps> = (props: ScopedProps<DialogPortalProps>) => {\n  const { __scopeDialog, forceMount, children, container } = props;\n  const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n  return (\n    <PortalProvider scope={__scopeDialog} forceMount={forceMount}>\n      {React.Children.map(children, (child) => (\n        <Presence present={forceMount || context.open}>\n          <PortalPrimitive asChild container={container}>\n            {child}\n          </PortalPrimitive>\n        </Presence>\n      ))}\n    </PortalProvider>\n  );\n};\n\nDialogPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogOverlay\n * -----------------------------------------------------------------------------------------------*/\n\nconst OVERLAY_NAME = 'DialogOverlay';\n\ntype DialogOverlayElement = DialogOverlayImplElement;\ninterface DialogOverlayProps extends DialogOverlayImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogOverlay = React.forwardRef<DialogOverlayElement, DialogOverlayProps>(\n  (props: ScopedProps<DialogOverlayProps>, forwardedRef) => {\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? (\n      <Presence present={forceMount || context.open}>\n        <DialogOverlayImpl {...overlayProps} ref={forwardedRef} />\n      </Presence>\n    ) : null;\n  }\n);\n\nDialogOverlay.displayName = OVERLAY_NAME;\n\ntype DialogOverlayImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DialogOverlayImplProps extends PrimitiveDivProps {}\n\nconst Slot = createSlot('DialogOverlay.RemoveScroll');\n\nconst DialogOverlayImpl = React.forwardRef<DialogOverlayImplElement, DialogOverlayImplProps>(\n  (props: ScopedProps<DialogOverlayImplProps>, forwardedRef) => {\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return (\n      // Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n      // ie. when `Overlay` and `Content` are siblings\n      <RemoveScroll as={Slot} allowPinchZoom shards={[context.contentRef]}>\n        <Primitive.div\n          data-state={getState(context.open)}\n          {...overlayProps}\n          ref={forwardedRef}\n          // We re-enable pointer-events prevented by `Dialog.Content` to allow scrolling the overlay.\n          style={{ pointerEvents: 'auto', ...overlayProps.style }}\n        />\n      </RemoveScroll>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DialogContent';\n\ntype DialogContentElement = DialogContentTypeElement;\ninterface DialogContentProps extends DialogContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogContent = React.forwardRef<DialogContentElement, DialogContentProps>(\n  (props: ScopedProps<DialogContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return (\n      <Presence present={forceMount || context.open}>\n        {context.modal ? (\n          <DialogContentModal {...contentProps} ref={forwardedRef} />\n        ) : (\n          <DialogContentNonModal {...contentProps} ref={forwardedRef} />\n        )}\n      </Presence>\n    );\n  }\n);\n\nDialogContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentTypeElement = DialogContentImplElement;\ninterface DialogContentTypeProps\n  extends Omit<DialogContentImplProps, 'trapFocus' | 'disableOutsidePointerEvents'> {}\n\nconst DialogContentModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, context.contentRef, contentRef);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      const content = contentRef.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure focus isn't trapped once `DialogContent` has been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        disableOutsidePointerEvents\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          event.preventDefault();\n          context.triggerRef.current?.focus();\n        })}\n        onPointerDownOutside={composeEventHandlers(props.onPointerDownOutside, (event) => {\n          const originalEvent = event.detail.originalEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n\n          // If the event is a right-click, we shouldn't close because\n          // it is effectively as if we right-clicked the `Overlay`.\n          if (isRightClick) event.preventDefault();\n        })}\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) =>\n          event.preventDefault()\n        )}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst DialogContentNonModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = React.useRef(false);\n    const hasPointerDownOutsideRef = React.useRef(false);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={forwardedRef}\n        trapFocus={false}\n        disableOutsidePointerEvents={false}\n        onCloseAutoFocus={(event) => {\n          props.onCloseAutoFocus?.(event);\n\n          if (!event.defaultPrevented) {\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            // Always prevent auto focus because we either focus manually or want user agent focus\n            event.preventDefault();\n          }\n\n          hasInteractedOutsideRef.current = false;\n          hasPointerDownOutsideRef.current = false;\n        }}\n        onInteractOutside={(event) => {\n          props.onInteractOutside?.(event);\n\n          if (!event.defaultPrevented) {\n            hasInteractedOutsideRef.current = true;\n            if (event.detail.originalEvent.type === 'pointerdown') {\n              hasPointerDownOutsideRef.current = true;\n            }\n          }\n\n          // Prevent dismissing when clicking the trigger.\n          // As the trigger is already setup to close, without doing so would\n          // cause it to close and immediately open.\n          const target = event.target as HTMLElement;\n          const targetIsTrigger = context.triggerRef.current?.contains(target);\n          if (targetIsTrigger) event.preventDefault();\n\n          // On Safari if the trigger is inside a container with tabIndex={0}, when clicked\n          // we will get the pointer down outside event on the trigger, but then a subsequent\n          // focus outside event on the container, we ignore any focus outside event when we've\n          // already had a pointer down outside event.\n          if (event.detail.originalEvent.type === 'focusin' && hasPointerDownOutsideRef.current) {\n            event.preventDefault();\n          }\n        }}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentImplElement = React.ComponentRef<typeof DismissableLayer>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\ninterface DialogContentImplProps extends Omit<DismissableLayerProps, 'onDismiss'> {\n  /**\n   * When `true`, focus cannot escape the `Content` via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n\n  /**\n   * Event handler called when auto-focusing on open.\n   * Can be prevented.\n   */\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n}\n\nconst DialogContentImpl = React.forwardRef<DialogContentImplElement, DialogContentImplProps>(\n  (props: ScopedProps<DialogContentImplProps>, forwardedRef) => {\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n\n    // Make sure the whole tree has focus guards as our `Dialog` will be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    return (\n      <>\n        <FocusScope\n          asChild\n          loop\n          trapped={trapFocus}\n          onMountAutoFocus={onOpenAutoFocus}\n          onUnmountAutoFocus={onCloseAutoFocus}\n        >\n          <DismissableLayer\n            role=\"dialog\"\n            id={context.contentId}\n            aria-describedby={context.descriptionId}\n            aria-labelledby={context.titleId}\n            data-state={getState(context.open)}\n            {...contentProps}\n            ref={composedRefs}\n            onDismiss={() => context.onOpenChange(false)}\n          />\n        </FocusScope>\n        {process.env.NODE_ENV !== 'production' && (\n          <>\n            <TitleWarning titleId={context.titleId} />\n            <DescriptionWarning contentRef={contentRef} descriptionId={context.descriptionId} />\n          </>\n        )}\n      </>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTitle\n * -----------------------------------------------------------------------------------------------*/\n\nconst TITLE_NAME = 'DialogTitle';\n\ntype DialogTitleElement = React.ComponentRef<typeof Primitive.h2>;\ntype PrimitiveHeading2Props = React.ComponentPropsWithoutRef<typeof Primitive.h2>;\ninterface DialogTitleProps extends PrimitiveHeading2Props {}\n\nconst DialogTitle = React.forwardRef<DialogTitleElement, DialogTitleProps>(\n  (props: ScopedProps<DialogTitleProps>, forwardedRef) => {\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return <Primitive.h2 id={context.titleId} {...titleProps} ref={forwardedRef} />;\n  }\n);\n\nDialogTitle.displayName = TITLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogDescription\n * -----------------------------------------------------------------------------------------------*/\n\nconst DESCRIPTION_NAME = 'DialogDescription';\n\ntype DialogDescriptionElement = React.ComponentRef<typeof Primitive.p>;\ntype PrimitiveParagraphProps = React.ComponentPropsWithoutRef<typeof Primitive.p>;\ninterface DialogDescriptionProps extends PrimitiveParagraphProps {}\n\nconst DialogDescription = React.forwardRef<DialogDescriptionElement, DialogDescriptionProps>(\n  (props: ScopedProps<DialogDescriptionProps>, forwardedRef) => {\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return <Primitive.p id={context.descriptionId} {...descriptionProps} ref={forwardedRef} />;\n  }\n);\n\nDialogDescription.displayName = DESCRIPTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogClose\n * -----------------------------------------------------------------------------------------------*/\n\nconst CLOSE_NAME = 'DialogClose';\n\ntype DialogCloseElement = React.ComponentRef<typeof Primitive.button>;\ninterface DialogCloseProps extends PrimitiveButtonProps {}\n\nconst DialogClose = React.forwardRef<DialogCloseElement, DialogCloseProps>(\n  (props: ScopedProps<DialogCloseProps>, forwardedRef) => {\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return (\n      <Primitive.button\n        type=\"button\"\n        {...closeProps}\n        ref={forwardedRef}\n        onClick={composeEventHandlers(props.onClick, () => context.onOpenChange(false))}\n      />\n    );\n  }\n);\n\nDialogClose.displayName = CLOSE_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst TITLE_WARNING_NAME = 'DialogTitleWarning';\n\nconst [WarningProvider, useWarningContext] = createContext(TITLE_WARNING_NAME, {\n  contentName: CONTENT_NAME,\n  titleName: TITLE_NAME,\n  docsSlug: 'dialog',\n});\n\ntype TitleWarningProps = { titleId?: string };\n\nconst TitleWarning: React.FC<TitleWarningProps> = ({ titleId }) => {\n  const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n\n  const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n\n  React.useEffect(() => {\n    if (titleId) {\n      const hasTitle = document.getElementById(titleId);\n      if (!hasTitle) console.error(MESSAGE);\n    }\n  }, [MESSAGE, titleId]);\n\n  return null;\n};\n\nconst DESCRIPTION_WARNING_NAME = 'DialogDescriptionWarning';\n\ntype DescriptionWarningProps = {\n  contentRef: React.RefObject<DialogContentElement | null>;\n  descriptionId?: string;\n};\n\nconst DescriptionWarning: React.FC<DescriptionWarningProps> = ({ contentRef, descriptionId }) => {\n  const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n  const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n\n  React.useEffect(() => {\n    const describedById = contentRef.current?.getAttribute('aria-describedby');\n    // if we have an id and the user hasn't set aria-describedby={undefined}\n    if (descriptionId && describedById) {\n      const hasDescription = document.getElementById(descriptionId);\n      if (!hasDescription) console.warn(MESSAGE);\n    }\n  }, [MESSAGE, contentRef, descriptionId]);\n\n  return null;\n};\n\nconst Root = Dialog;\nconst Trigger = DialogTrigger;\nconst Portal = DialogPortal;\nconst Overlay = DialogOverlay;\nconst Content = DialogContent;\nconst Title = DialogTitle;\nconst Description = DialogDescription;\nconst Close = DialogClose;\n\nexport {\n  createDialogScope,\n  //\n  Dialog,\n  DialogTrigger,\n  DialogPortal,\n  DialogOverlay,\n  DialogContent,\n  DialogTitle,\n  DialogDescription,\n  DialogClose,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Overlay,\n  Content,\n  Title,\n  Description,\n  Close,\n  //\n  WarningProvider,\n};\nexport type {\n  DialogProps,\n  DialogTriggerProps,\n  DialogPortalProps,\n  DialogOverlayProps,\n  DialogContentProps,\n  DialogTitleProps,\n  DialogDescriptionProps,\n  DialogCloseProps,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,uBAAuB;AAChC,SAAS,eAAe,0BAA0B;AAClD,SAAS,aAAa;AACtB,SAAS,4BAA4B;AACrC,SAAS,wBAAwB;AACjC,SAAS,kBAAkB;AAC3B,SAAS,UAAU,uBAAuB;AAC1C,SAAS,gBAAgB;AACzB,SAAS,iBAAiB;AAC1B,SAAS,sBAAsB;AAC/B,SAAS,oBAAoB;AAC7B,SAAS,kBAAkB;AAC3B,SAAS,kBAAkB;AAsDvB,SA2VM,UA3VN,KA2VM,YA3VN;;;;;;;;;;;;;;;;;;AA9CJ,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,CAAA,8KAAI,qBAAA,EAAmB,WAAW;AAc/E,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,WAAW;AAU9F,IAAM,SAAgC,CAAC,UAAoC;IACzE,MAAM,EACJ,aAAA,EACA,QAAA,EACA,MAAM,QAAA,EACN,WAAA,EACA,YAAA,EACA,QAAQ,IAAA,EACV,GAAI;IACJ,MAAM,+KAAmB,SAAA,EAA0B,IAAI;IACvD,MAAM,+KAAmB,SAAA,EAA6B,IAAI;IAC1D,MAAM,CAAC,MAAM,OAAO,CAAA,mMAAI,uBAAA,EAAqB;QAC3C,MAAM;QACN,aAAa,eAAe;QAC5B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,gBAAA;QACC,OAAO;QACP;QACA;QACA,iLAAW,QAAA,CAAM;QACjB,+KAAS,QAAA,CAAM;QACf,qLAAe,QAAA,CAAM;QACrB;QACA,cAAc;QACd,gLAAoB,cAAA;kCAAY,IAAM;0CAAQ,CAAC,WAAa,CAAC,QAAQ;;iCAAG;YAAC,OAAO;SAAC;QACjF;QAEC;IAAA;AAGP;AAEA,OAAO,WAAA,GAAc;AAMrB,IAAM,eAAe;AAMrB,IAAM,kLAAsB,aAAA,EAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EAAE,aAAA,EAAe,GAAG,aAAa,CAAA,GAAI;IAC3C,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,MAAM,wMAAqB,kBAAA,EAAgB,cAAc,QAAQ,UAAU;IAC3E,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,MAAA,EAAV;QACC,MAAK;QACL,iBAAc;QACd,iBAAe,QAAQ,IAAA;QACvB,iBAAe,QAAQ,SAAA;QACvB,cAAY,SAAS,QAAQ,IAAI;QAChC,GAAG,YAAA;QACJ,KAAK;QACL,6KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,QAAQ,YAAY;IAAA;AAGvE;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,cAAc;AAGpB,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,aAAa;IAC9F,YAAY,KAAA;AACd,CAAC;AAgBD,IAAM,eAA4C,CAAC,UAA0C;IAC3F,MAAM,EAAE,aAAA,EAAe,UAAA,EAAY,QAAA,EAAU,SAAA,CAAU,CAAA,GAAI;IAC3D,MAAM,UAAU,iBAAiB,aAAa,aAAa;IAC3D,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,gBAAA;QAAe,OAAO;QAAe;QACnC,wKAAM,WAAA,CAAS,GAAA,CAAI,UAAU,CAAC,QAC7B,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;gBAAS,SAAS,cAAc,QAAQ,IAAA;gBACvC,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,wKAAC,SAAA,EAAA;oBAAgB,SAAO;oBAAC;oBACtB,UAAA;gBAAA,CACH;YAAA,CACF,CACD;IAAA,CACH;AAEJ;AAEA,aAAa,WAAA,GAAc;AAM3B,IAAM,eAAe;AAWrB,IAAM,iLAAsB,cAAA,EAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,gBAAgB,iBAAiB,cAAc,MAAM,aAAa;IACxE,MAAM,EAAE,aAAa,cAAc,UAAA,EAAY,GAAG,aAAa,CAAA,GAAI;IACnE,MAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;IAClE,OAAO,QAAQ,KAAA,GACb,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;QAAS,SAAS,cAAc,QAAQ,IAAA;QACvC,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,mBAAA;YAAmB,GAAG,YAAA;YAAc,KAAK;QAAA,CAAc;IAAA,CAC1D,IACE;AACN;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,WAAO,iLAAA,EAAW,4BAA4B;AAEpD,IAAM,sLAA0B,aAAA,EAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,aAAA,EAAe,GAAG,aAAa,CAAA,GAAI;IAC3C,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,OAAA,oFAAA;IAAA,gDAAA;IAGE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,wOAAA,EAAA;QAAa,IAAI;QAAM,gBAAc;QAAC,QAAQ;YAAC,QAAQ,UAAU;SAAA;QAChE,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;YACC,cAAY,SAAS,QAAQ,IAAI;YAChC,GAAG,YAAA;YACJ,KAAK;YAEL,OAAO;gBAAE,eAAe;gBAAQ,GAAG,aAAa,KAAA;YAAM;QAAA;IACxD,CACF;AAEJ;AAOF,IAAM,eAAe;AAWrB,IAAM,iLAAsB,cAAA,EAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,gBAAgB,iBAAiB,cAAc,MAAM,aAAa;IACxE,MAAM,EAAE,aAAa,cAAc,UAAA,EAAY,GAAG,aAAa,CAAA,GAAI;IACnE,MAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;IAClE,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;QAAS,SAAS,cAAc,QAAQ,IAAA;QACtC,UAAA,QAAQ,KAAA,GACP,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,oBAAA;YAAoB,GAAG,YAAA;YAAc,KAAK;QAAA,CAAc,IAEzD,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,uBAAA;YAAuB,GAAG,YAAA;YAAc,KAAK;QAAA,CAAc;IAAA,CAEhE;AAEJ;AAGF,cAAc,WAAA,GAAc;AAQ5B,IAAM,uLAA2B,aAAA,EAC/B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;IAClE,MAAM,+KAAmB,SAAA,EAAuB,IAAI;IACpD,MAAM,kMAAe,kBAAA,EAAgB,cAAc,QAAQ,UAAA,EAAY,UAAU;sKAG3E,YAAA;wCAAU,MAAM;YACpB,MAAM,UAAU,WAAW,OAAA;YAC3B,IAAI,QAAS,CAAA,wKAAO,aAAA,EAAW,OAAO;QACxC;uCAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,mBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QAGL,WAAW,QAAQ,IAAA;QACnB,6BAA2B;QAC3B,sBAAkB,uLAAA,EAAqB,MAAM,gBAAA,EAAkB,CAAC,UAAU;YACxE,MAAM,cAAA,CAAe;YACrB,QAAQ,UAAA,CAAW,OAAA,EAAS,MAAM;QACpC,CAAC;QACD,uBAAsB,0LAAA,EAAqB,MAAM,oBAAA,EAAsB,CAAC,UAAU;YAChF,MAAM,gBAAgB,MAAM,MAAA,CAAO,aAAA;YACnC,MAAM,gBAAgB,cAAc,MAAA,KAAW,KAAK,cAAc,OAAA,KAAY;YAC9E,MAAM,eAAe,cAAc,MAAA,KAAW,KAAK;YAInD,IAAI,aAAc,CAAA,MAAM,cAAA,CAAe;QACzC,CAAC;QAGD,gBAAgB,2LAAA,EAAqB,MAAM,cAAA,EAAgB,CAAC,QAC1D,MAAM,cAAA,CAAe;IACvB;AAGN;AAKF,IAAM,4BAA8B,2KAAA,EAClC,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;IAClE,MAAM,4LAAgC,SAAA,EAAO,KAAK;IAClD,MAAM,6LAAiC,SAAA,EAAO,KAAK;IAEnD,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,mBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QACL,WAAW;QACX,6BAA6B;QAC7B,kBAAkB,CAAC,UAAU;YAC3B,MAAM,gBAAA,GAAmB,KAAK;YAE9B,IAAI,CAAC,MAAM,gBAAA,EAAkB;gBAC3B,IAAI,CAAC,wBAAwB,OAAA,CAAS,CAAA,QAAQ,UAAA,CAAW,OAAA,EAAS,MAAM;gBAExE,MAAM,cAAA,CAAe;YACvB;YAEA,wBAAwB,OAAA,GAAU;YAClC,yBAAyB,OAAA,GAAU;QACrC;QACA,mBAAmB,CAAC,UAAU;YAC5B,MAAM,iBAAA,GAAoB,KAAK;YAE/B,IAAI,CAAC,MAAM,gBAAA,EAAkB;gBAC3B,wBAAwB,OAAA,GAAU;gBAClC,IAAI,MAAM,MAAA,CAAO,aAAA,CAAc,IAAA,KAAS,eAAe;oBACrD,yBAAyB,OAAA,GAAU;gBACrC;YACF;YAKA,MAAM,SAAS,MAAM,MAAA;YACrB,MAAM,kBAAkB,QAAQ,UAAA,CAAW,OAAA,EAAS,SAAS,MAAM;YACnE,IAAI,gBAAiB,CAAA,MAAM,cAAA,CAAe;YAM1C,IAAI,MAAM,MAAA,CAAO,aAAA,CAAc,IAAA,KAAS,aAAa,yBAAyB,OAAA,EAAS;gBACrF,MAAM,cAAA,CAAe;YACvB;QACF;IAAA;AAGN;AA6BF,IAAM,sLAA0B,aAAA,EAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,aAAA,EAAe,SAAA,EAAW,eAAA,EAAiB,gBAAA,EAAkB,GAAG,aAAa,CAAA,GAAI;IACzF,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,MAAM,+KAAmB,SAAA,EAAuB,IAAI;IACpD,MAAM,iMAAe,mBAAA,EAAgB,cAAc,UAAU;IAI7D,CAAA,GAAA,8KAAA,CAAA,iBAAA,CAAe;IAEf,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,OAAA,EAAA,sKAAA,CAAA,WAAA,EAAA;QACE,UAAA;YAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,gLAAC,aAAA,EAAA;gBACC,SAAO;gBACP,MAAI;gBACJ,SAAS;gBACT,kBAAkB;gBAClB,oBAAoB;gBAEpB,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,sLAAC,mBAAA,EAAA;oBACC,MAAK;oBACL,IAAI,QAAQ,SAAA;oBACZ,oBAAkB,QAAQ,aAAA;oBAC1B,mBAAiB,QAAQ,OAAA;oBACzB,cAAY,SAAS,QAAQ,IAAI;oBAChC,GAAG,YAAA;oBACJ,KAAK;oBACL,WAAW,IAAM,QAAQ,YAAA,CAAa,KAAK;gBAAA;YAC7C;YAGA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,OAAA,EAAA,sKAAA,CAAA,WAAA,EAAA;gBACE,UAAA;oBAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,cAAA;wBAAa,SAAS,QAAQ,OAAA;oBAAA,CAAS;oBACxC,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,oBAAA;wBAAmB;wBAAwB,eAAe,QAAQ,aAAA;oBAAA,CAAe;iBAAA;YAAA,CACpF;SAAA;IAAA,CAEJ;AAEJ;AAOF,IAAM,aAAa;AAMnB,IAAM,eAAoB,8KAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,EAAA,EAAV;QAAa,IAAI,QAAQ,OAAA;QAAU,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAC/E;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,mBAAmB;AAMzB,IAAM,qBAA0B,8KAAA,EAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,aAAA,EAAe,GAAG,iBAAiB,CAAA,GAAI;IAC/C,MAAM,UAAU,iBAAiB,kBAAkB,aAAa;IAChE,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,CAAA,EAAV;QAAY,IAAI,QAAQ,aAAA;QAAgB,GAAG,gBAAA;QAAkB,KAAK;IAAA,CAAc;AAC1F;AAGF,kBAAkB,WAAA,GAAc;AAMhC,IAAM,aAAa;AAKnB,IAAM,gLAAoB,aAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,MAAA,EAAV;QACC,MAAK;QACJ,GAAG,UAAA;QACJ,KAAK;QACL,UAAS,0LAAA,EAAqB,MAAM,OAAA,EAAS,IAAM,QAAQ,YAAA,CAAa,KAAK,CAAC;IAAA;AAGpF;AAGF,YAAY,WAAA,GAAc;AAI1B,SAAS,SAAS,IAAA,EAAe;IAC/B,OAAO,OAAO,SAAS;AACzB;AAEA,IAAM,qBAAqB;AAE3B,IAAM,CAAC,iBAAiB,iBAAiB,CAAA,IAAI,0LAAA,EAAc,oBAAoB;IAC7E,aAAa;IACb,WAAW;IACX,UAAU;AACZ,CAAC;AAID,IAAM,eAA4C,CAAC,EAAE,OAAA,CAAQ,CAAA,KAAM;IACjE,MAAM,sBAAsB,kBAAkB,kBAAkB;IAEhE,MAAM,UAAU,CAAA,EAAA,EAAK,oBAAoB,WAAW,CAAA,gBAAA,EAAmB,oBAAoB,SAAS,CAAA;;0BAAA,EAE1E,oBAAoB,SAAS,CAAA;;0EAAA,EAEmB,oBAAoB,QAAQ,EAAA;sKAEhG,YAAA;kCAAU,MAAM;YACpB,IAAI,SAAS;gBACX,MAAM,WAAW,SAAS,cAAA,CAAe,OAAO;gBAChD,IAAI,CAAC,SAAU,CAAA,QAAQ,KAAA,CAAM,OAAO;YACtC;QACF;iCAAG;QAAC;QAAS,OAAO;KAAC;IAErB,OAAO;AACT;AAEA,IAAM,2BAA2B;AAOjC,IAAM,qBAAwD,CAAC,EAAE,UAAA,EAAY,aAAA,CAAc,CAAA,KAAM;IAC/F,MAAM,4BAA4B,kBAAkB,wBAAwB;IAC5E,MAAM,UAAU,CAAA,0EAAA,EAA6E,0BAA0B,WAAW,CAAA,EAAA,CAAA;sKAE5H,YAAA;wCAAU,MAAM;YACpB,MAAM,gBAAgB,WAAW,OAAA,EAAS,aAAa,kBAAkB;YAEzE,IAAI,iBAAiB,eAAe;gBAClC,MAAM,iBAAiB,SAAS,cAAA,CAAe,aAAa;gBAC5D,IAAI,CAAC,eAAgB,CAAA,QAAQ,IAAA,CAAK,OAAO;YAC3C;QACF;uCAAG;QAAC;QAAS;QAAY,aAAa;KAAC;IAEvC,OAAO;AACT;AAEA,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,QAAQ;AACd,IAAM,cAAc;AACpB,IAAM,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "file": "x.js", "sources": ["file:///D:/code/play/book-management-system/book-frontend/node_modules/lucide-react/src/icons/x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M18 6 6 18', key: '1bl5f8' }],\n  ['path', { d: 'm6 6 12 12', key: 'd8bk6v' }],\n];\n\n/**\n * @component @name X\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggNiA2IDE4IiAvPgogIDxwYXRoIGQ9Im02IDYgMTIgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst X = createLucideIcon('x', __iconNode);\n\nexport default X;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,EAAK,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/play/book-management-system/book-frontend/node_modules/%40radix-ui/react-alert-dialog/src/alert-dialog.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\nimport { createDialogScope } from '@radix-ui/react-dialog';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createSlottable } from '@radix-ui/react-slot';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialog\n * -----------------------------------------------------------------------------------------------*/\n\nconst ROOT_NAME = 'AlertDialog';\n\ntype ScopedProps<P> = P & { __scopeAlertDialog?: Scope };\nconst [createAlertDialogContext, createAlertDialogScope] = createContextScope(ROOT_NAME, [\n  createDialogScope,\n]);\nconst useDialogScope = createDialogScope();\n\ntype DialogProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Root>;\ninterface AlertDialogProps extends Omit<DialogProps, 'modal'> {}\n\nconst AlertDialog: React.FC<AlertDialogProps> = (props: ScopedProps<AlertDialogProps>) => {\n  const { __scopeAlertDialog, ...alertDialogProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return <DialogPrimitive.Root {...dialogScope} {...alertDialogProps} modal={true} />;\n};\n\nAlertDialog.displayName = ROOT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogTrigger\n * -----------------------------------------------------------------------------------------------*/\nconst TRIGGER_NAME = 'AlertDialogTrigger';\n\ntype AlertDialogTriggerElement = React.ComponentRef<typeof DialogPrimitive.Trigger>;\ntype DialogTriggerProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Trigger>;\ninterface AlertDialogTriggerProps extends DialogTriggerProps {}\n\nconst AlertDialogTrigger = React.forwardRef<AlertDialogTriggerElement, AlertDialogTriggerProps>(\n  (props: ScopedProps<AlertDialogTriggerProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...triggerProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Trigger {...dialogScope} {...triggerProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'AlertDialogPortal';\n\ntype DialogPortalProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Portal>;\ninterface AlertDialogPortalProps extends DialogPortalProps {}\n\nconst AlertDialogPortal: React.FC<AlertDialogPortalProps> = (\n  props: ScopedProps<AlertDialogPortalProps>\n) => {\n  const { __scopeAlertDialog, ...portalProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return <DialogPrimitive.Portal {...dialogScope} {...portalProps} />;\n};\n\nAlertDialogPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogOverlay\n * -----------------------------------------------------------------------------------------------*/\n\nconst OVERLAY_NAME = 'AlertDialogOverlay';\n\ntype AlertDialogOverlayElement = React.ComponentRef<typeof DialogPrimitive.Overlay>;\ntype DialogOverlayProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>;\ninterface AlertDialogOverlayProps extends DialogOverlayProps {}\n\nconst AlertDialogOverlay = React.forwardRef<AlertDialogOverlayElement, AlertDialogOverlayProps>(\n  (props: ScopedProps<AlertDialogOverlayProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...overlayProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Overlay {...dialogScope} {...overlayProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogOverlay.displayName = OVERLAY_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'AlertDialogContent';\n\ntype AlertDialogContentContextValue = {\n  cancelRef: React.MutableRefObject<AlertDialogCancelElement | null>;\n};\n\nconst [AlertDialogContentProvider, useAlertDialogContentContext] =\n  createAlertDialogContext<AlertDialogContentContextValue>(CONTENT_NAME);\n\ntype AlertDialogContentElement = React.ComponentRef<typeof DialogPrimitive.Content>;\ntype DialogContentProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>;\ninterface AlertDialogContentProps\n  extends Omit<DialogContentProps, 'onPointerDownOutside' | 'onInteractOutside'> {}\n\nconst Slottable = createSlottable('AlertDialogContent');\n\nconst AlertDialogContent = React.forwardRef<AlertDialogContentElement, AlertDialogContentProps>(\n  (props: ScopedProps<AlertDialogContentProps>, forwardedRef) => {\n    const { __scopeAlertDialog, children, ...contentProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    const contentRef = React.useRef<AlertDialogContentElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n    const cancelRef = React.useRef<AlertDialogCancelElement | null>(null);\n\n    return (\n      <DialogPrimitive.WarningProvider\n        contentName={CONTENT_NAME}\n        titleName={TITLE_NAME}\n        docsSlug=\"alert-dialog\"\n      >\n        <AlertDialogContentProvider scope={__scopeAlertDialog} cancelRef={cancelRef}>\n          <DialogPrimitive.Content\n            role=\"alertdialog\"\n            {...dialogScope}\n            {...contentProps}\n            ref={composedRefs}\n            onOpenAutoFocus={composeEventHandlers(contentProps.onOpenAutoFocus, (event) => {\n              event.preventDefault();\n              cancelRef.current?.focus({ preventScroll: true });\n            })}\n            onPointerDownOutside={(event) => event.preventDefault()}\n            onInteractOutside={(event) => event.preventDefault()}\n          >\n            {/**\n             * We have to use `Slottable` here as we cannot wrap the `AlertDialogContentProvider`\n             * around everything, otherwise the `DescriptionWarning` would be rendered straight away.\n             * This is because we want the accessibility checks to run only once the content is actually\n             * open and that behaviour is already encapsulated in `DialogContent`.\n             */}\n            <Slottable>{children}</Slottable>\n            {process.env.NODE_ENV === 'development' && (\n              <DescriptionWarning contentRef={contentRef} />\n            )}\n          </DialogPrimitive.Content>\n        </AlertDialogContentProvider>\n      </DialogPrimitive.WarningProvider>\n    );\n  }\n);\n\nAlertDialogContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogTitle\n * -----------------------------------------------------------------------------------------------*/\n\nconst TITLE_NAME = 'AlertDialogTitle';\n\ntype AlertDialogTitleElement = React.ComponentRef<typeof DialogPrimitive.Title>;\ntype DialogTitleProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>;\ninterface AlertDialogTitleProps extends DialogTitleProps {}\n\nconst AlertDialogTitle = React.forwardRef<AlertDialogTitleElement, AlertDialogTitleProps>(\n  (props: ScopedProps<AlertDialogTitleProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...titleProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Title {...dialogScope} {...titleProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogTitle.displayName = TITLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogDescription\n * -----------------------------------------------------------------------------------------------*/\n\nconst DESCRIPTION_NAME = 'AlertDialogDescription';\n\ntype AlertDialogDescriptionElement = React.ComponentRef<typeof DialogPrimitive.Description>;\ntype DialogDescriptionProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>;\ninterface AlertDialogDescriptionProps extends DialogDescriptionProps {}\n\nconst AlertDialogDescription = React.forwardRef<\n  AlertDialogDescriptionElement,\n  AlertDialogDescriptionProps\n>((props: ScopedProps<AlertDialogDescriptionProps>, forwardedRef) => {\n  const { __scopeAlertDialog, ...descriptionProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return <DialogPrimitive.Description {...dialogScope} {...descriptionProps} ref={forwardedRef} />;\n});\n\nAlertDialogDescription.displayName = DESCRIPTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogAction\n * -----------------------------------------------------------------------------------------------*/\n\nconst ACTION_NAME = 'AlertDialogAction';\n\ntype AlertDialogActionElement = React.ComponentRef<typeof DialogPrimitive.Close>;\ntype DialogCloseProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Close>;\ninterface AlertDialogActionProps extends DialogCloseProps {}\n\nconst AlertDialogAction = React.forwardRef<AlertDialogActionElement, AlertDialogActionProps>(\n  (props: ScopedProps<AlertDialogActionProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...actionProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Close {...dialogScope} {...actionProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogAction.displayName = ACTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogCancel\n * -----------------------------------------------------------------------------------------------*/\n\nconst CANCEL_NAME = 'AlertDialogCancel';\n\ntype AlertDialogCancelElement = React.ComponentRef<typeof DialogPrimitive.Close>;\ninterface AlertDialogCancelProps extends DialogCloseProps {}\n\nconst AlertDialogCancel = React.forwardRef<AlertDialogCancelElement, AlertDialogCancelProps>(\n  (props: ScopedProps<AlertDialogCancelProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...cancelProps } = props;\n    const { cancelRef } = useAlertDialogContentContext(CANCEL_NAME, __scopeAlertDialog);\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    const ref = useComposedRefs(forwardedRef, cancelRef);\n    return <DialogPrimitive.Close {...dialogScope} {...cancelProps} ref={ref} />;\n  }\n);\n\nAlertDialogCancel.displayName = CANCEL_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype DescriptionWarningProps = {\n  contentRef: React.RefObject<AlertDialogContentElement | null>;\n};\n\nconst DescriptionWarning: React.FC<DescriptionWarningProps> = ({ contentRef }) => {\n  const MESSAGE = `\\`${CONTENT_NAME}\\` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the \\`${CONTENT_NAME}\\` by passing a \\`${DESCRIPTION_NAME}\\` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an \\`id\\` and passing the same value to the \\`aria-describedby\\` prop in \\`${CONTENT_NAME}\\`. If the description is confusing or duplicative for sighted users, you can use the \\`@radix-ui/react-visually-hidden\\` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;\n\n  React.useEffect(() => {\n    const hasDescription = document.getElementById(\n      contentRef.current?.getAttribute('aria-describedby')!\n    );\n    if (!hasDescription) console.warn(MESSAGE);\n  }, [MESSAGE, contentRef]);\n\n  return null;\n};\n\nconst Root = AlertDialog;\nconst Trigger = AlertDialogTrigger;\nconst Portal = AlertDialogPortal;\nconst Overlay = AlertDialogOverlay;\nconst Content = AlertDialogContent;\nconst Action = AlertDialogAction;\nconst Cancel = AlertDialogCancel;\nconst Title = AlertDialogTitle;\nconst Description = AlertDialogDescription;\n\nexport {\n  createAlertDialogScope,\n  //\n  AlertDialog,\n  AlertDialogTrigger,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogContent,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Overlay,\n  Content,\n  Action,\n  Cancel,\n  Title,\n  Description,\n};\nexport type {\n  AlertDialogProps,\n  AlertDialogTriggerProps,\n  AlertDialogPortalProps,\n  AlertDialogOverlayProps,\n  AlertDialogContentProps,\n  AlertDialogActionProps,\n  AlertDialogCancelProps,\n  AlertDialogTitleProps,\n  AlertDialogDescriptionProps,\n};\n"], "names": ["Root", "<PERSON><PERSON>", "Portal", "Overlay", "Content", "Title", "Description"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,0BAA0B;AACnC,SAAS,uBAAuB;AAChC,YAAY,qBAAqB;AAEjC,SAAS,4BAA4B;AACrC,SAAS,uBAAuB;AAsBvB,cAkGC,YAlGD;;;;;;;;;;AAdT,IAAM,YAAY;AAGlB,IAAM,CAAC,0BAA0B,sBAAsB,CAAA,8KAAI,qBAAA,EAAmB,WAAW;0KACvF,oBAAA;CACD;AACD,IAAM,2LAAiB,oBAAA,CAAkB;AAKzC,IAAM,cAA0C,CAAC,UAAyC;IACxF,MAAM,EAAE,kBAAA,EAAoB,GAAG,iBAAiB,CAAA,GAAI;IACpD,MAAM,cAAc,eAAe,kBAAkB;IACrD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,wKAAiB,OAAA,EAAhB;QAAsB,GAAG,WAAA;QAAc,GAAG,gBAAA;QAAkB,OAAO;IAAA,CAAM;AACnF;AAEA,YAAY,WAAA,GAAc;AAK1B,IAAM,eAAe;AAMrB,IAAM,uLAA2B,aAAA,EAC/B,CAAC,OAA6C,iBAAiB;IAC7D,MAAM,EAAE,kBAAA,EAAoB,GAAG,aAAa,CAAA,GAAI;IAChD,MAAM,cAAc,eAAe,kBAAkB;IACrD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,wKAAiB,UAAA,EAAhB;QAAyB,GAAG,WAAA;QAAc,GAAG,YAAA;QAAc,KAAK;IAAA,CAAc;AACxF;AAGF,mBAAmB,WAAA,GAAc;AAMjC,IAAM,cAAc;AAKpB,IAAM,oBAAsD,CAC1D,UACG;IACH,MAAM,EAAE,kBAAA,EAAoB,GAAG,YAAY,CAAA,GAAI;IAC/C,MAAM,cAAc,eAAe,kBAAkB;IACrD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,wKAAiB,SAAA,EAAhB;QAAwB,GAAG,WAAA;QAAc,GAAG,WAAA;IAAA,CAAa;AACnE;AAEA,kBAAkB,WAAA,GAAc;AAMhC,IAAM,eAAe;AAMrB,IAAM,yBAA2B,2KAAA,EAC/B,CAAC,OAA6C,iBAAiB;IAC7D,MAAM,EAAE,kBAAA,EAAoB,GAAG,aAAa,CAAA,GAAI;IAChD,MAAM,cAAc,eAAe,kBAAkB;IACrD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,wKAAiB,UAAA,EAAhB;QAAyB,GAAG,WAAA;QAAc,GAAG,YAAA;QAAc,KAAK;IAAA,CAAc;AACxF;AAGF,mBAAmB,WAAA,GAAc;AAMjC,IAAM,eAAe;AAMrB,IAAM,CAAC,4BAA4B,4BAA4B,CAAA,GAC7D,yBAAyD,YAAY;AAOvE,IAAM,oLAAY,kBAAA,EAAgB,oBAAoB;AAEtD,IAAM,uLAA2B,aAAA,EAC/B,CAAC,OAA6C,iBAAiB;IAC7D,MAAM,EAAE,kBAAA,EAAoB,QAAA,EAAU,GAAG,aAAa,CAAA,GAAI;IAC1D,MAAM,cAAc,eAAe,kBAAkB;IACrD,MAAM,+KAAmB,SAAA,EAAkC,IAAI;IAC/D,MAAM,eAAe,qMAAA,EAAgB,cAAc,UAAU;IAC7D,MAAM,8KAAkB,SAAA,EAAwC,IAAI;IAEpE,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,uKAAiB,mBAAA,EAAhB;QACC,aAAa;QACb,WAAW;QACX,UAAS;QAET,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,4BAAA;YAA2B,OAAO;YAAoB;YACrD,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,OAAA,wKAAiB,UAAA,EAAhB;gBACC,MAAK;gBACJ,GAAG,WAAA;gBACH,GAAG,YAAA;gBACJ,KAAK;gBACL,kBAAiB,0LAAA,EAAqB,aAAa,eAAA,EAAiB,CAAC,UAAU;oBAC7E,MAAM,cAAA,CAAe;oBACrB,UAAU,OAAA,EAAS,MAAM;wBAAE,eAAe;oBAAK,CAAC;gBAClD,CAAC;gBACD,sBAAsB,CAAC,QAAU,MAAM,cAAA,CAAe;gBACtD,mBAAmB,CAAC,QAAU,MAAM,cAAA,CAAe;gBAQnD,UAAA;oBAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAA;wBAAW;oBAAA,CAAS;oBAEnB,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,oBAAA;wBAAmB;oBAAA,CAAwB;iBAAA;YAAA;QAEhD,CACF;IAAA;AAGN;AAGF,mBAAmB,WAAA,GAAc;AAMjC,IAAM,aAAa;AAMnB,IAAM,qLAAyB,aAAA,EAC7B,CAAC,OAA2C,iBAAiB;IAC3D,MAAM,EAAE,kBAAA,EAAoB,GAAG,WAAW,CAAA,GAAI;IAC9C,MAAM,cAAc,eAAe,kBAAkB;IACrD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,wKAAiB,QAAA,EAAhB;QAAuB,GAAG,WAAA;QAAc,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AACpF;AAGF,iBAAiB,WAAA,GAAc;AAM/B,IAAM,mBAAmB;AAMzB,IAAM,2LAA+B,aAAA,EAGnC,CAAC,OAAiD,iBAAiB;IACnE,MAAM,EAAE,kBAAA,EAAoB,GAAG,iBAAiB,CAAA,GAAI;IACpD,MAAM,cAAc,eAAe,kBAAkB;IACrD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,wKAAiB,cAAA,EAAhB;QAA6B,GAAG,WAAA;QAAc,GAAG,gBAAA;QAAkB,KAAK;IAAA,CAAc;AAChG,CAAC;AAED,uBAAuB,WAAA,GAAc;AAMrC,IAAM,cAAc;AAMpB,IAAM,sLAA0B,aAAA,EAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,kBAAA,EAAoB,GAAG,YAAY,CAAA,GAAI;IAC/C,MAAM,cAAc,eAAe,kBAAkB;IACrD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,wKAAiB,QAAA,EAAhB;QAAuB,GAAG,WAAA;QAAc,GAAG,WAAA;QAAa,KAAK;IAAA,CAAc;AACrF;AAGF,kBAAkB,WAAA,GAAc;AAMhC,IAAM,cAAc;AAKpB,IAAM,qBAA0B,8KAAA,EAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,kBAAA,EAAoB,GAAG,YAAY,CAAA,GAAI;IAC/C,MAAM,EAAE,SAAA,CAAU,CAAA,GAAI,6BAA6B,aAAa,kBAAkB;IAClF,MAAM,cAAc,eAAe,kBAAkB;IACrD,MAAM,yLAAM,kBAAA,EAAgB,cAAc,SAAS;IACnD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,wKAAiB,QAAA,EAAhB;QAAuB,GAAG,WAAA;QAAc,GAAG,WAAA;QAAa;IAAA,CAAU;AAC5E;AAGF,kBAAkB,WAAA,GAAc;AAQhC,IAAM,qBAAwD,CAAC,EAAE,UAAA,CAAW,CAAA,KAAM;IAChF,MAAM,UAAU,CAAA,EAAA,EAAK,YAAY,CAAA;;mCAAA,EAEE,YAAY,CAAA,kBAAA,EAAqB,gBAAgB,CAAA;;0JAAA,EAEsE,YAAY,CAAA;;sFAAA,CAAA;sKAIhK,YAAA;wCAAU,MAAM;YACpB,MAAM,iBAAiB,SAAS,cAAA,CAC9B,WAAW,OAAA,EAAS,aAAa,kBAAkB;YAErD,IAAI,CAAC,eAAgB,CAAA,QAAQ,IAAA,CAAK,OAAO;QAC3C;uCAAG;QAAC;QAAS,UAAU;KAAC;IAExB,OAAO;AACT;AAEA,IAAMA,QAAO;AACb,IAAMC,WAAU;AAChB,IAAMC,UAAS;AACf,IAAMC,WAAU;AAChB,IAAMC,WAAU;AAChB,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAMC,SAAQ;AACd,IAAMC,eAAc", "ignoreList": [0], "debugId": null}}]}