import { Hono } from 'hono';
import { db } from '../db';
import { books } from '../db/schema';
import { eq, and, like, sql, desc } from 'drizzle-orm';
import { authMiddleware, requireRole } from '../middleware/auth';

const booksApi = new Hono();

// 为所有 /api/books 的路由应用基础认证
booksApi.use('/*', authMiddleware);

// 1. 获取图书列表 (所有登录用户)
// GET /api/books?page=1&limit=10&title=Hono
booksApi.get('/', async (c) => {
  const page = parseInt(c.req.query('page') || '1');
  const limit = parseInt(c.req.query('limit') || '10');
  const offset = (page - 1) * limit;
  const title = c.req.query('title');
  const author = c.req.query('author');

  const conditions = [];
  if (title) conditions.push(like(books.title, `%${title}%`));
  if (author) conditions.push(like(books.author, `%${author}%`));
  
  const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

  const data = await db.query.books.findMany({
    where: whereClause,
    orderBy: [desc(books.createdAt)],
    limit: limit,
    offset: offset,
  });

  const totalResult = await db.select({ count: sql<number>`count(*)` }).from(books).where(whereClause);
  const total = totalResult[0].count;

  return c.json({
    data,
    meta: { total, page, limit, totalPages: Math.ceil(total / limit) },
  });
});

// 2. 获取单本图书详情 (所有登录用户)
booksApi.get('/:id', async (c) => {
    const id = parseInt(c.req.param('id'));
    const book = await db.query.books.findFirst({ where: eq(books.id, id) });
    if (!book) return c.json({ error: '图书未找到' }, 404);
    return c.json(book);
});

// 3. 创建图书 (仅限管理员)
booksApi.post('/', requireRole('admin'), async (c) => {
  const body = await c.req.json();
  const newBook = await db.insert(books).values(body).returning();
  return c.json(newBook[0], 201);
});

// 4. 更新图书 (仅限管理员)
booksApi.put('/:id', requireRole('admin'), async (c) => {
  const id = parseInt(c.req.param('id'));
  const body = await c.req.json();
  const updatedBook = await db.update(books).set(body).where(eq(books.id, id)).returning();
  if (updatedBook.length === 0) return c.json({ error: '图书未找到' }, 404);
  return c.json(updatedBook[0]);
});

// 5. 删除图书 (仅限管理员)
booksApi.delete('/:id', requireRole('admin'), async (c) => {
  const id = parseInt(c.req.param('id'));
  const deletedBook = await db.delete(books).where(eq(books.id, id)).returning();
  if (deletedBook.length === 0) return c.json({ error: '图书未找到' }, 404);
  return c.json({ message: '删除成功' });
});

export default booksApi;