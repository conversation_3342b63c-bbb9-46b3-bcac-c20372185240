"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function HomePage() {
  const router = useRouter();

  useEffect(() => {
    // 检查是否已登录
    const token = localStorage.getItem("token");
    if (token) {
      // 如果已登录，直接跳转到仪表板
      router.push("/dashboard");
    }
  }, [router]);

  const handleLogin = () => {
    router.push("/login");
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-100 dark:bg-gray-950">
      <Card className="mx-auto max-w-md w-full">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl">📚 图书管理系统</CardTitle>
          <CardDescription>
            欢迎使用现代化的图书管理系统
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center text-sm text-muted-foreground">
            <p>功能特性：</p>
            <ul className="mt-2 space-y-1">
              <li>• 图书信息管理</li>
              <li>• 用户权限控制</li>
              <li>• 现代化界面设计</li>
              <li>• 响应式布局</li>
            </ul>
          </div>
          <Button onClick={handleLogin} className="w-full" size="lg">
            开始使用
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}