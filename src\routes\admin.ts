import { Hono } from 'hono';
import { db } from '../db';
import { users } from '../db/schema';
import { eq } from 'drizzle-orm';
import { authMiddleware, requireRole } from '../middleware/auth';
import type { Variables } from '../types/hono';

const adminApi = new Hono<{ Variables: Variables }>();

// 为所有 /api/admin 路由应用"必须是管理员"的规则
adminApi.use('/*', authMiddleware, requireRole('admin'));

// 1. 获取所有用户列表
adminApi.get('/users', async (c) => {
  const allUsers = await db.select({
    id: users.id,
    username: users.username,
    nickname: users.nickname,
    role: users.role,
    status: users.status,
    createdAt: users.createdAt,
  }).from(users);
  return c.json(allUsers);
});

// 2. 创建新用户
adminApi.post('/users', async (c) => {
    const { username, nickname, password, role } = await c.req.json();

    if (!username || !password) {
        return c.json({ error: '用户名和密码不能为空' }, 400);
    }

    if (password.length < 6) {
        return c.json({ error: '密码至少需要6位' }, 400);
    }

    // 检查用户名是否已存在
    const existingUser = await db.select().from(users).where(eq(users.username, username)).limit(1);
    if (existingUser.length > 0) {
        return c.json({ error: '用户名已存在' }, 400);
    }

    const passwordHash = await Bun.password.hash(password);
    const newUser = await db.insert(users).values({
        username,
        nickname: nickname || username,
        passwordHash,
        role: role || 'user',
        status: 'active',
    }).returning({
        id: users.id,
        username: users.username,
        nickname: users.nickname,
        role: users.role,
        status: users.status,
        createdAt: users.createdAt,
    });

    return c.json(newUser[0], 201);
});

// 3. 更新用户信息 (昵称, 角色, 状态)
adminApi.put('/users/:id', async (c) => {
    const id = parseInt(c.req.param('id'));
    const adminId = c.get('userId');
    if (id === adminId) return c.json({ error: '管理员不能修改自己的信息' }, 400);

    const { nickname, role, status } = await c.req.json();
    const updatedUser = await db.update(users).set({ nickname, role, status }).where(eq(users.id, id)).returning();
    if (updatedUser.length === 0) return c.json({ error: '用户未找到' }, 404);
    return c.json(updatedUser[0]);
});

// 3. 为用户重置密码
adminApi.put('/users/:id/reset-password', async (c) => {
    const id = parseInt(c.req.param('id'));
    const { newPassword } = await c.req.json();
    if (!newPassword || newPassword.length < 6) return c.json({ error: '新密码至少6位' }, 400);

    const passwordHash = await Bun.password.hash(newPassword);
    await db.update(users).set({ passwordHash }).where(eq(users.id, id));
    return c.json({ message: '密码重置成功' });
});

// 4. 删除用户
adminApi.delete('/users/:id', async (c) => {
    const id = parseInt(c.req.param('id'));
    const adminId = c.get('userId');
    if (id === adminId) return c.json({ error: '不能删除自己' }, 400);
    
    await db.delete(users).where(eq(users.id, id));
    return c.json({ message: '用户删除成功' });
});

export default adminApi;