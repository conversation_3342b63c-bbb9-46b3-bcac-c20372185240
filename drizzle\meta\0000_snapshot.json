{"version": "6", "dialect": "sqlite", "id": "1539c1cb-b998-40c4-b7f5-7f378d189e1e", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"books": {"name": "books", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "author": {"name": "author", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "published_year": {"name": "published_year", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'\"2025-06-27T03:27:02.265Z\"'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "nickname": {"name": "nickname", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "password_hash": {"name": "password_hash", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'user'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'\"2025-06-27T03:27:02.265Z\"'"}}, "indexes": {"users_username_unique": {"name": "users_username_unique", "columns": ["username"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}