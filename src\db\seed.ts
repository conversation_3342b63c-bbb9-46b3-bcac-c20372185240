import { db } from './index';
import { users } from './schema';
import { eq } from 'drizzle-orm';

// 警告：在真实项目中，密码绝不能硬编码，应使用环境变量！
const ADMIN_USERNAME = 'admin';
const ADMIN_PASSWORD = 'admin123root';

async function seed() {
  console.log('🌱 Seeding database...');

  const existingAdmin = await db.query.users.findFirst({
    where: eq(users.username, ADMIN_USERNAME),
  });

  if (existingAdmin) {
    console.log('✅ Admin user already exists. Skipping.');
  } else {
    const passwordHash = await Bun.password.hash(ADMIN_PASSWORD, {
      algorithm: 'bcrypt',
      cost: 10,
    });

    await db.insert(users).values({
      username: ADMIN_USERNAME,
      nickname: '超级管理员',
      passwordHash: passwordHash,
      role: 'admin', // 关键！
      status: 'active',
    });
    console.log(`✅ Admin user '${ADMIN_USERNAME}' created.`);
  }

  console.log('Seeding complete.');
}

seed().catch((err) => {
  console.error('❌ Seeding failed:', err);
  process.exit(1);
});