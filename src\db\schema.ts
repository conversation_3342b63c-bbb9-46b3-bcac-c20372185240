import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';

// 定义角色和状态的可用值，这提供了类型安全
export const userRoles = ['admin', 'user'] as const;
export const userStatuses = ['active', 'disabled'] as const;

// 用户表
export const users = sqliteTable('users', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  username: text('username').notNull().unique(),
  nickname: text('nickname'), // 昵称，可以为空
  passwordHash: text('password_hash').notNull(),
  role: text('role', { enum: userRoles }).notNull().default('user'),
  status: text('status', { enum: userStatuses }).notNull().default('active'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().default(new Date()),
});

// 图书表
export const books = sqliteTable('books', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  title: text('title').notNull(),
  author: text('author').notNull(),
  publishedYear: integer('published_year'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().default(new Date()),
});