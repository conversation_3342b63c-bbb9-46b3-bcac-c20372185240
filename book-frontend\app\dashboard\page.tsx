"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, PlusCircle, Edit } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";

// 定义图书类型
type Book = {
  id: number;
  title: string;
  author: string;
  publishedYear: number;
};

export default function BooksPage() {
  const [books, setBooks] = useState<Book[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingBook, setEditingBook] = useState<Book | null>(null);
  const [newBook, setNewBook] = useState({
    title: "",
    author: "",
    publishedYear: new Date().getFullYear(),
  });
  const router = useRouter();
  // 可以在这里添加分页和搜索的状态

  useEffect(() => {
    // 检查登录状态
    const token = localStorage.getItem("token");
    if (!token) {
      router.push("/login");
      return;
    }

    const fetchBooks = async () => {
      setIsLoading(true);
      try {
        const response = await fetch("http://localhost:5000/api/books", {
          headers: {
            "Authorization": `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          if (response.status === 401) {
            // Token 过期或无效，跳转到登录页
            localStorage.removeItem("token");
            router.push("/login");
            return;
          }
          throw new Error("获取图书失败");
        }

        const data = await response.json();
        setBooks(data.data); // Hono API 返回的数据在 data.data 里
      } catch (err: any) {
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBooks();
  }, [router]);



  const handleDeleteBook = async (bookId: number, title: string) => {
    try {
      const token = localStorage.getItem("token");

      const response = await fetch(`http://localhost:5000/api/books/${bookId}`, {
        method: 'DELETE',
        headers: {
          "Authorization": `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error("删除图书失败");
      }

      // 重新获取图书列表
      setBooks(books.filter(book => book.id !== bookId));
      toast.success("图书删除成功");
    } catch (err: any) {
      toast.error(`删除失败: ${err.message}`);
    }
  };

  const handleEditBook = (book: Book) => {
    setEditingBook(book);
    setIsEditDialogOpen(true);
  };

  const handleUpdateBook = async () => {
    if (!editingBook || !editingBook.title.trim() || !editingBook.author.trim()) {
      toast.error("请填写图书标题和作者");
      return;
    }

    try {
      const token = localStorage.getItem("token");

      const response = await fetch(`http://localhost:5000/api/books/${editingBook.id}`, {
        method: 'PUT',
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title: editingBook.title,
          author: editingBook.author,
          publishedYear: editingBook.publishedYear,
        }),
      });

      if (!response.ok) {
        throw new Error("更新图书失败");
      }

      const updatedBook = await response.json();
      setBooks(books.map(book => book.id === editingBook.id ? updatedBook : book));
      setIsEditDialogOpen(false);
      setEditingBook(null);
      toast.success("图书更新成功");
    } catch (err: any) {
      toast.error(`更新失败: ${err.message}`);
    }
  };

  const handleAddBook = async () => {
    if (!newBook.title.trim() || !newBook.author.trim()) {
      toast.error("请填写图书标题和作者");
      return;
    }

    try {
      const token = localStorage.getItem("token");

      const response = await fetch("http://localhost:5000/api/books", {
        method: 'POST',
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newBook),
      });

      if (!response.ok) {
        throw new Error("添加图书失败");
      }

      const addedBook = await response.json();
      setBooks([addedBook, ...books]);
      setNewBook({
        title: "",
        author: "",
        publishedYear: new Date().getFullYear(),
      });
      setIsAddDialogOpen(false);
      toast.success("图书添加成功");
    } catch (err: any) {
      toast.error(`添加失败: ${err.message}`);
    }
  };

  // 过滤图书列表
  const filteredBooks = books.filter(book =>
    book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    book.author.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoading) return <div>加载中...</div>;
  if (error) return <div>错误: {error}</div>;

  return (
    <>
      <div className="flex items-center">
        <h1 className="text-lg font-semibold md:text-2xl">图书列表</h1>
        <div className="ml-auto flex items-center gap-2">
            <Input
              placeholder="搜索书名或作者..."
              className="w-64"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm" className="h-8 gap-1">
                  <PlusCircle className="h-3.5 w-3.5" />
                  <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
                    新增图书
                  </span>
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>新增图书</DialogTitle>
                  <DialogDescription>
                    添加新的图书到系统中。请填写完整的图书信息。
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="title" className="text-right">
                      标题
                    </Label>
                    <Input
                      id="title"
                      value={newBook.title}
                      onChange={(e) => setNewBook({...newBook, title: e.target.value})}
                      className="col-span-3"
                      placeholder="请输入图书标题"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="author" className="text-right">
                      作者
                    </Label>
                    <Input
                      id="author"
                      value={newBook.author}
                      onChange={(e) => setNewBook({...newBook, author: e.target.value})}
                      className="col-span-3"
                      placeholder="请输入作者姓名"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="year" className="text-right">
                      出版年份
                    </Label>
                    <Input
                      id="year"
                      type="number"
                      value={newBook.publishedYear}
                      onChange={(e) => setNewBook({...newBook, publishedYear: parseInt(e.target.value) || new Date().getFullYear()})}
                      className="col-span-3"
                      placeholder="请输入出版年份"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" onClick={handleAddBook}>
                    添加图书
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

        </div>
      </div>
      <div className="rounded-lg border shadow-sm">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>书名</TableHead>
              <TableHead>作者</TableHead>
              <TableHead>出版年份</TableHead>
              <TableHead>
                <span className="sr-only">操作</span>
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredBooks.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-8">
                  {searchTerm ? "没有找到匹配的图书" : "暂无图书数据"}
                </TableCell>
              </TableRow>
            ) : (
              filteredBooks.map((book) => (
                <TableRow key={book.id}>
                  <TableCell className="font-medium">{book.title}</TableCell>
                  <TableCell>{book.author}</TableCell>
                  <TableCell>{book.publishedYear}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button aria-haspopup="true" size="icon" variant="ghost">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Toggle menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>操作</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => handleEditBook(book)}>
                          <Edit className="mr-2 h-4 w-4" />
                          编辑
                        </DropdownMenuItem>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <DropdownMenuItem
                              className="text-red-600"
                              onSelect={(e) => e.preventDefault()}
                            >
                              删除
                            </DropdownMenuItem>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>确认删除</AlertDialogTitle>
                              <AlertDialogDescription>
                                确定要删除图书 "{book.title}" 吗？此操作不可撤销。
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>取消</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDeleteBook(book.id, book.title)}
                                className="bg-red-600 hover:bg-red-700"
                              >
                                删除
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* 编辑图书对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>编辑图书</DialogTitle>
            <DialogDescription>
              修改图书信息。点击保存来更新图书。
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-title" className="text-right">
                标题
              </Label>
              <Input
                id="edit-title"
                value={editingBook?.title || ""}
                onChange={(e) => setEditingBook(editingBook ? {...editingBook, title: e.target.value} : null)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-author" className="text-right">
                作者
              </Label>
              <Input
                id="edit-author"
                value={editingBook?.author || ""}
                onChange={(e) => setEditingBook(editingBook ? {...editingBook, author: e.target.value} : null)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-year" className="text-right">
                出版年份
              </Label>
              <Input
                id="edit-year"
                type="number"
                value={editingBook?.publishedYear || new Date().getFullYear()}
                onChange={(e) => setEditingBook(editingBook ? {...editingBook, publishedYear: parseInt(e.target.value)} : null)}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" onClick={handleUpdateBook}>保存更改</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 在这里可以添加分页组件 */}
    </>
  );
}