import { Hono } from 'hono';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';

import authApi from './routes/auth';
import booksApi from './routes/books';
import adminApi from './routes/admin';

const app = new Hono();

// 全局中间件
app.use('*', logger()); // 记录日志
app.use('*', prettyJSON()); // 格式化 JSON 输出

// 欢迎路由
app.get('/', (c) => {
  return c.text('📚 欢迎来到 Hono 图书管理系统 API');
});

// 挂载各个功能的路由
app.route('/api/auth', authApi);
app.route('/api/books', booksApi);
app.route('/api/admin', adminApi);

// 错误处理
app.notFound((c) => c.json({ error: 'Not Found' }, 404));
app.onError((err, c) => {
  console.error(err);
  return c.json({ error: 'Internal Server Error' }, 500);
});

export default {
  port: 5000,
  fetch: app.fetch,
};