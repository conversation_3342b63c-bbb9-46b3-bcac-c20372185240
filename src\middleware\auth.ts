import { createMiddleware } from 'hono/factory';
import { jwt } from 'hono/jwt';
import { userRoles } from '../db/schema';

const secret = process.env.JWT_SECRET || 'a-very-secret-key-for-dev';

// 中间件1: 基础认证
// 作用：验证 Token 是否有效，并将 payload 存入 context
export const authMiddleware = createMiddleware(async (c, next) => {
  const jwtMiddleware = jwt({ secret });
  const response = await jwtMiddleware(c, async () => {}); // 执行 jwt 中间件，但不立即调用 next
  if (response?.status === 401) {
      return c.json({ error: '未授权或 Token 已过期' }, 401);
  }

  const payload = c.get('jwtPayload');
  c.set('userId', payload.sub);
  c.set('userRole', payload.role);
  
  await next();
});

// 中间件2: 角色要求
// 作用：检查用户是否具有特定角色
export const requireRole = (role: typeof userRoles[number]) => {
  return createMiddleware(async (c, next) => {
    const userRole = c.get('userRole');
    if (userRole !== role) {
      return c.json({ error: '权限不足' }, 403);
    }
    await next();
  });
};