import { Hono } from 'hono';
import { db } from '../db';
import { users } from '../db/schema';
import { eq } from 'drizzle-orm';
import { sign } from 'hono/jwt';

const authApi = new Hono();

// 1. 用户注册 (仅限普通用户)
authApi.post('/register', async (c) => {
  const { username, password, nickname } = await c.req.json();

  if (!username || !password || password.length < 6) {
    return c.json({ error: '无效的用户名或密码（密码至少6位）' }, 400);
  }

  const existingUser = await db.query.users.findFirst({ where: eq(users.username, username) });
  if (existingUser) {
    return c.json({ error: '用户名已存在' }, 409);
  }

  const passwordHash = await Bun.password.hash(password, { algorithm: 'bcrypt' });
  await db.insert(users).values({ username, passwordHash, nickname }); // role 和 status 会使用 schema 中的默认值

  return c.json({ message: '注册成功' }, 201);
});

// 2. 用户登录
authApi.post('/login', async (c) => {
  const { username, password } = await c.req.json();
  const user = await db.query.users.findFirst({ where: eq(users.username, username) });

  if (!user) {
    return c.json({ error: '用户名错误' }, 401);
  }

  if (user.status === 'disabled') {
    return c.json({ error: '该账户已被禁用' }, 403);
  }

  const isPasswordValid = await Bun.password.verify(password, user.passwordHash);
  if (!isPasswordValid) {
    return c.json({ error: '密码错误' }, 401);
  }

  // 准备 JWT 的 payload
  const payload = {
    sub: user.id, // Subject (用户ID)
    role: user.role, // 关键：将角色信息放入 Token
    exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24, // 24小时后过期
  };
  const secret = process.env.JWT_SECRET || 'a-very-secret-key-for-dev';
  const token = await sign(payload, secret);

  return c.json({ token });
});

export default authApi;